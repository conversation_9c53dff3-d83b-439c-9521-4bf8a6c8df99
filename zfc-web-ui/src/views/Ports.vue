<template>
  <div class="ports-container">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <h3>Port Forwarding</h3>
          <div class="header-actions">
            <el-button
              @click="toggleSearchExpanded"
              :icon="searchExpanded ? ArrowUp : ArrowDown"
              type="default"
              class="search-toggle-btn"
              :aria-expanded="searchExpanded"
              :aria-controls="'search-form-container'"
              :title="searchExpanded ? 'Hide search filters' : 'Show search filters'"
            >
              <span class="search-toggle-text">
                {{ searchExpanded ? 'Hide Search' : 'Show Search' }}
              </span>
              <el-badge
                v-if="!searchExpanded && hasActiveFilters"
                :value="activeFiltersCount"
                class="filter-badge"
                type="primary"
              />
            </el-button>
            <el-button type="primary" :icon="Plus" @click="handleAddPort">
              Add Port
            </el-button>
          </div>
        </div>
      </template>

      <!-- Collapsible Search Form -->
      <el-collapse-transition>
        <div
          v-show="searchExpanded"
          class="search-container"
          id="search-form-container"
          role="region"
          aria-label="Search and filter options"
        >
          <el-form :model="searchForm" class="search-form" :inline="true">
            <el-form-item label="ID">
              <el-input
                v-model="searchForm.id"
                placeholder="Search by exact ID"
                clearable
                style="width: 120px"
                @input="handleSearchInput"
              />
            </el-form-item>
            <el-form-item label="Name">
              <el-input
                v-model="searchForm.name"
                placeholder="Search by name (regex supported)"
                clearable
                style="width: 200px"
                @input="handleSearchInput"
              />
            </el-form-item>
            <el-form-item label="Line">
              <el-select
                v-model="searchForm.line"
                placeholder="Select line"
                clearable
                filterable
                style="width: 150px"
                @change="handleSearchChange"
                :filter-method="filterLines"
                default-first-option
                reserve-keyword
              >
                <el-option
                  v-for="line in filteredLines"
                  :key="line.id"
                  :label="line.display_name"
                  :value="line.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="Entry Point">
              <el-input
                v-model="searchForm.entryPoint"
                placeholder="Search entry point (regex supported)"
                clearable
                style="width: 200px"
                @input="handleSearchInput"
              />
            </el-form-item>
            <el-form-item label="Target">
              <el-input
                v-model="searchForm.target"
                placeholder="Search target (regex supported)"
                clearable
                style="width: 200px"
                @input="handleSearchInput"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                Search
              </el-button>
              <el-button @click="handleClearSearch">
                Clear
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-collapse-transition>

      <!-- Port Management Table -->
      <div class="table-card">
        <el-table
          :data="ports"
          v-loading="loading"
          style="width: 100%"
          :header-cell-style="headerCellStyle"
          class="port-table"
          :row-class-name="({ row }) => row.is_suspended ? 'suspended-port' : ''"
          :row-style="{ height: '60px' }"
        >
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column label="Name" width="180">
          <template #default="{ row }">
            <el-tooltip
              placement="top"
              :content="row.display_name"
              effect="light"
              :disabled="!shouldShowTooltip(row.display_name, 160)"
              popper-class="truncated-text-tooltip"
            >
              <div
                :class="['truncated-text', { 'text-truncated': shouldShowTooltip(row.display_name, 160) }]"
                :ref="el => setTextRef(el, row.id + '_name')"
              >
                {{ row.display_name }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="Status" width="70">
          <template #default="{ row }">
            <!-- Status indicator with color coding -->
            <div class="status-indicator">
              <div
                :class="['status-dot', row.is_suspended ? 'status-suspended' : 'status-active']"
                :title="row.is_suspended ? 'Suspended' : 'Active'"
              ></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="Line" width="100">
          <template #default="{ row }">
            <el-tooltip
              placement="top"
              :content="getLineName(row.outbound_endpoint_id)"
              effect="light"
              :disabled="!shouldShowTooltip(getLineName(row.outbound_endpoint_id), 130)"
              popper-class="truncated-text-tooltip"
            >
              <div
                :class="['truncated-text', { 'text-truncated': shouldShowTooltip(getLineName(row.outbound_endpoint_id), 130) }]"
                :ref="el => setTextRef(el, row.id + '_line')"
              >
                {{ getLineName(row.outbound_endpoint_id) }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="Entry Point" width="200">
          <template #default="{ row }">
            <el-tooltip
              placement="top"
              :content="`${row.ip_addr}:${row.port_v4}`"
              effect="light"
              :disabled="!shouldShowTooltip(`${row.ip_addr}:${row.port_v4}`, 160)"
              popper-class="truncated-text-tooltip"
            >
              <div
                :class="['truncated-text', { 'text-truncated': shouldShowTooltip(`${row.ip_addr}:${row.port_v4}`, 160) }]"
                :ref="el => setTextRef(el, row.id + '_entry')"
              >
                {{ row.ip_addr }}:{{ row.port_v4 }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="Target" width="200">
          <template #default="{ row }">
            <el-tooltip
              placement="top"
              :content="row.target_address_list.join('\n')"
              effect="light"
              :visible="tooltipVisibleMap.get(row.id + '_target')"
              popper-class="target-addresses-tooltip"
            >
              <div
                class="target-address truncated-text"
                @mouseenter="() => handleMouseEnter(row, 'target')"
                @mouseleave="() => handleMouseLeave(row, 'target')"
              >
                {{ row.target_address_list[0] }}
                <span v-if="row.target_address_list.length > 1" class="more-addresses">
                  (+{{ row.target_address_list.length - 1 }} more)
                </span>
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="Select Mode" width="100">
          <template #default="{ row }">
            <el-tag size="small" type="info" v-if="row.target_select_mode !== null">
              {{ getTargetModeLabel(row.target_select_mode) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="Test Method" width="100">
          <template #default="{ row }">
            <el-tag size="small" type="info" v-if="row.test_method !== null">
              {{ getTestMethodLabel(row.test_method) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="Traffic" width="120">
          <template #default="{ row }">
            <div class="traffic-container">
              <div class="traffic-row">
                <span class="traffic-icon">↑</span>
                <span class="traffic-value">{{ formatTraffic(row.traffic_out) }}</span>
              </div>
              <div class="traffic-row">
                <span class="traffic-icon">↓</span>
                <span class="traffic-value">{{ formatTraffic(row.traffic_in) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="Actions" width="200">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-tooltip
                content="Modify port"
                placement="top"
                effect="light"
                manual
                :visible="tooltipVisibleMap.get(row.id + '_modify')"
                popper-class="custom-tooltip"
              >
                <el-button
                  type="primary"
                  :icon="Edit"
                  circle
                  @click="handleModify(row)"
                  @mouseenter="() => handleMouseEnter(row, 'modify')"
                  @mouseleave="() => handleMouseLeave(row, 'modify')"
                />
              </el-tooltip>

              <el-tooltip
                content="Test latency"
                placement="top"
                effect="light"
                manual
                :visible="tooltipVisibleMap.get(row.id + '_test')"
                popper-class="custom-tooltip"
              >
                <el-button
                  type="info"
                  :icon="Timer"
                  circle
                  :loading="testingLatencyIds.has(row.id)"
                  @click="handleTestLatency(row)"
                  @mouseenter="() => handleMouseEnter(row, 'test')"
                  @mouseleave="() => handleMouseLeave(row, 'test')"
                />
              </el-tooltip>

              <el-tooltip
                :content="row.is_suspended ? 'Resume port' : 'Suspend port'"
                placement="top"
                effect="light"
                manual
                :visible="tooltipVisibleMap.get(row.id + '_suspend')"
                popper-class="custom-tooltip"
              >
                <el-button
                  :type="row.is_suspended ? 'success' : 'warning'"
                  :icon="row.is_suspended ? CaretRight : VideoPause"
                  circle
                  :loading="suspendingPortIds.has(row.id)"
                  @click="handleSuspendResume(row)"
                  @mouseenter="() => handleMouseEnter(row, 'suspend')"
                  @mouseleave="() => handleMouseLeave(row, 'suspend')"
                />
              </el-tooltip>

              <el-tooltip
                content="Delete port"
                placement="top"
                effect="light"
                manual
                :visible="tooltipVisibleMap.get(row.id + '_delete')"
                popper-class="custom-tooltip"
              >
                <el-button
                  type="danger"
                  :icon="Delete"
                  circle
                  @click="handleDelete(row)"
                  @mouseenter="() => handleMouseEnter(row, 'delete')"
                  @mouseleave="() => handleMouseLeave(row, 'delete')"
                />
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      </div>

      <!-- Pagination Controls -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>Total {{ totalItems }} items</span>
          <el-select
            v-model="pageSize"
            @change="handlePageSizeChange"
            class="page-size-selector"
            size="small"
          >
            <el-option
              v-for="size in pageSizeOptions"
              :key="size"
              :label="`${size} / page`"
              :value="size"
            />
          </el-select>
        </div>
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalItems"
          :page-count="totalPages"
          layout="prev, pager, next, jumper"
          @current-change="handlePageChange"
          class="pagination-controls"
          small
        />
      </div>
    </el-card>

    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? 'Edit Port Forward' : 'Add Port Forward'"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="handleDialogClosed"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="140px"
        class="port-form"
      >
        <el-form-item label="Name" prop="display_name">
          <el-input v-model="form.display_name" placeholder="Enter name" />
        </el-form-item>
        <el-form-item label="Line" prop="outbound_endpoint_id">
          <el-select 
            v-model="form.outbound_endpoint_id" 
            placeholder="Select line"
            @change="handleLineChange"
            filterable
            style="width: 100%"
          >
            <el-option
              v-for="line in lines"
              :key="line.id"
              :label="line.display_name"
              :value="line.id"
            >
              <span>{{ line.display_name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">
                {{ line.ip_addr }}
              </span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Target Addresses" prop="target_address_list">
          <el-input
            type="textarea"
            v-model="targetAddressText"
            :rows="4"
            placeholder="Enter target addresses, one per line&#10;Example:&#10;example.com:80&#10;***********:8080 # comment"
            @input="handleTargetAddressInput"
          />
        </el-form-item>
        <el-form-item label="Target Select Mode" prop="target_select_mode">
          <el-select v-model="form.target_select_mode" placeholder="Select mode" style="width: 100%">
            <el-option label="Best Latency" :value="0" />
            <el-option label="Fallback" :value="1" />
            <el-option label="Balance (Domain Follow)" :value="2" />
            <el-option label="Balance (Round Robin)" :value="3" />
            <el-option label="Balance (Random)" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="Test Method" prop="test_method">
          <el-select v-model="form.test_method" placeholder="Select test method" style="width: 100%">
            <el-option label="TCP Ping" :value="0" />
            <el-option label="ICMP" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="Expected Port" prop="expected_port">
          <el-input
            v-model="form.expected_port"
            :placeholder="portRangePlaceholder"
          />
        </el-form-item>
        <el-form-item v-if="subscriptionStore.allowForwardEndpoint && showForwardEndpoints" label="Forward Endpoints" prop="forward_endpoints">
          <el-select
            v-model="selectedForwardEndpointIds"
            multiple
            filterable
            placeholder="Select forward endpoints"
            style="width: 100%"
            @change="handleForwardEndpointChange"
          >
            <el-option
              v-for="endpoint in forwardEndpoints"
              :key="endpoint.id"
              :label="endpoint.name"
              :value="endpoint.id"
            >
              <span>{{ endpoint.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="selectedForwardEndpoints.length > 0" label="Priority Order">
          <div class="forward-endpoints-list">
            <draggable
              v-model="selectedForwardEndpoints"
              @end="handleForwardEndpointSort"
              item-key="id"
              :animation="150"
              class="draggable-list"
            >
              <template #item="{ element }">
                <div class="forward-endpoint-item">
                  <el-tag 
                    class="endpoint-tag" 
                    size="large"
                    closable
                    effect="plain"
                    @close="handleRemoveEndpoint(element)"
                  >
                    <el-icon class="drag-handle"><Operation /></el-icon>
                    {{ element.name }}
                  </el-tag>
                </div>
              </template>
            </draggable>
          </div>
        </el-form-item>

        <el-form-item v-if="selectedForwardEndpoints.length > 1" label="Balance Strategy" prop="balance_strategy">
          <el-select
            v-model="form.balance_strategy"
            placeholder="Select balance strategy"
            style="width: 100%"
          >
            <el-option
              v-for="strategy in balanceStrategies"
              :key="strategy.value"
              :label="strategy.label"
              :value="strategy.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="subscriptionStore.allowForwardEndpoint && showForwardEndpoints" label="Tot Servers" prop="tot_server_list">
          <el-select
            v-model="selectedTotServerIds"
            multiple
            filterable
            placeholder="Select tot servers"
            style="width: 100%"
            @change="handleTotServerChange"
          >
            <el-option
              v-for="endpoint in availableTotServers"
              :key="endpoint.id"
              :label="endpoint.name"
              :value="endpoint.id"
            >
              <span>{{ endpoint.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="selectedTotServers.length > 0" label="Priority Order">
          <div class="forward-endpoints-list">
            <draggable
              v-model="selectedTotServers"
              @end="handleTotServerSort"
              item-key="id"
              :animation="150"
              class="draggable-list"
            >
              <template #item="{ element }">
                <div class="forward-endpoint-item">
                  <el-tag 
                    class="endpoint-tag" 
                    size="large"
                    closable
                    effect="plain"
                    @close="handleRemoveTotServer(element)"
                  >
                    <el-icon class="drag-handle"><Operation /></el-icon>
                    {{ element.name }}
                  </el-tag>
                </div>
              </template>
            </draggable>
          </div>
        </el-form-item>

        <el-form-item v-if="selectedTotServers.length > 1" label="Balance Strategy" prop="balance_strategy">
          <el-select
            v-model="form.tot_server_select_mode"
            placeholder="Select balance strategy"
            style="width: 100%"
          >
            <el-option
              v-for="strategy in balanceStrategies"
              :key="strategy.value"
              :label="strategy.label"
              :value="strategy.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="selectedTotServers.length > 1" label="Tot Server Test Method" prop="tot_server_test_method">
          <el-select v-model="form.tot_server_test_method" placeholder="Select test method" style="width: 100%">
            <el-option label="TCP Ping" :value="0" />
            <el-option label="ICMP" :value="1" />
          </el-select>
        </el-form-item>
        
        <!-- Network Topology Diagram -->
        <div class="network-topology" v-if="form.outbound_endpoint_id">
          <div class="topology-title">
            <el-icon><Connection /></el-icon>
            <span>Network Topology</span>
          </div>
          <div class="topology-container">
            <svg class="topology-svg" :viewBox="'0 0 800 ' + Math.max(120, selectedForwardEndpoints.length * 80)">
              <!-- 定义箭头 -->
              <defs>
                <marker
                  id="arrow"
                  viewBox="0 0 10 10"
                  refX="9"
                  refY="5"
                  markerWidth="6"
                  markerHeight="6"
                  orient="auto">
                  <path d="M 0 0 L 10 5 L 0 10 z" class="arrow-path"/>
                </marker>
              </defs>

              <!-- Line Node -->
              <g class="node line-node">
                <rect x="50" 
                      :y="selectedForwardEndpoints.length ? (centerY - 20) : 40" 
                      width="120" 
                      height="40" 
                      rx="5" />
                <text x="110" 
                      :y="selectedForwardEndpoints.length ? (centerY + 5) : 65" 
                      text-anchor="middle">{{ getLineName(form.outbound_endpoint_id) }}</text>
              </g>

              <!-- Forward Endpoints -->
              <template v-if="selectedForwardEndpoints.length > 0">
                <g v-for="(endpoint, index) in selectedForwardEndpoints" :key="endpoint.id">
                  <!-- Endpoint Node -->
                  <g class="node endpoint-node">
                    <rect x="340" 
                          :y="startY + index * 60 - 20" 
                          width="120" 
                          height="40" 
                          rx="5" />
                    <text x="400" 
                          :y="startY + index * 60 + 5" 
                          text-anchor="middle">{{ endpoint.name }}</text>
                  </g>

                  <!-- Left Connection -->
                  <path :d="'M 170 ' + centerY + 
                           ' L 250 ' + centerY + 
                           ' L 250 ' + (startY + index * 60) + 
                           ' L 340 ' + (startY + index * 60)"
                        class="connection-line" 
                        marker-end="url(#arrow)" />
                  
                  <!-- Right Connection -->
                  <path :d="'M 460 ' + (startY + index * 60) + 
                           ' L 550 ' + (startY + index * 60) + 
                           ' L 550 ' + centerY + 
                           ' L 630 ' + centerY"
                        class="connection-line" 
                        marker-end="url(#arrow)" />
                </g>
              </template>
              <template v-else>
                <!-- Direct Connection -->
                <path d="M 170 60 L 630 60" class="connection-line" marker-end="url(#arrow)" />
              </template>

              <!-- Target Node -->
              <g class="node target-node">
                <rect x="630" 
                      :y="selectedForwardEndpoints.length ? (centerY - 20) : 40" 
                      width="120" 
                      height="40" 
                      rx="5" />
                <text x="690" 
                      :y="selectedForwardEndpoints.length ? (centerY + 5) : 65" 
                      text-anchor="middle">
                  {{ form.target_address_list[0] }}:{{ form.target_port_v4 }}
                </text>
              </g>
            </svg>
          </div>
        </div>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="handleCreate" :loading="creating">
            Create
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="modifyDialogVisible"
      title="Modify Port Forward"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="handleModifyDialogClosed"
    >
      <el-form
        ref="modifyFormRef"
        :model="modifyForm"
        :rules="rules"
        label-width="140px"
        class="port-form"
      >
        <el-form-item label="Name" prop="display_name">
          <el-input v-model="modifyForm.display_name" placeholder="Enter name" />
        </el-form-item>
        <el-form-item label="Target Addresses" prop="target_address_list">
          <el-input
            type="textarea"
            v-model="modifyTargetAddressText"
            :rows="4"
            placeholder="Enter target addresses, one per line&#10;Example:&#10;example.com:80&#10;***********:8080"
            @input="handleModifyTargetAddressInput"
          />
        </el-form-item>
        <el-form-item label="Target Select Mode" prop="target_select_mode">
          <el-select v-model="modifyForm.target_select_mode" placeholder="Select mode" style="width: 100%">
            <el-option label="Best Latency" :value="0" />
            <el-option label="Fallback" :value="1" />
            <el-option label="Balance (Domain Follow)" :value="2" />
            <el-option label="Balance (Round Robin)" :value="3" />
            <el-option label="Balance (Random)" :value="4" />
          </el-select>
        </el-form-item>
        <el-form-item label="Test Method" prop="test_method">
          <el-select v-model="modifyForm.test_method" placeholder="Select test method" style="width: 100%">
            <el-option label="TCP Ping" :value="0" />
            <el-option label="ICMP" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="Expected Port" prop="expected_port">
          <el-input
            v-model="modifyForm.expected_port"
            :placeholder="portRangePlaceholder"
          />
        </el-form-item>
        <el-form-item v-if="subscriptionStore.allowForwardEndpoint && showForwardEndpoints" label="Forward Endpoints" prop="forward_endpoints">
          <el-select
            v-model="selectedForwardEndpointIds"
            multiple
            filterable
            placeholder="Select forward endpoints"
            style="width: 100%"
            @change="handleForwardEndpointChange"
          >
            <el-option
              v-for="endpoint in forwardEndpoints"
              :key="endpoint.id"
              :label="endpoint.name"
              :value="endpoint.id"
            >
              <span>{{ endpoint.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="selectedForwardEndpoints.length > 0" label="Priority Order">
          <div class="forward-endpoints-list">
            <draggable
              v-model="selectedForwardEndpoints"
              @end="handleForwardEndpointSort"
              item-key="id"
              :animation="150"
              class="draggable-list"
            >
              <template #item="{ element }">
                <div class="forward-endpoint-item">
                  <el-tag 
                    class="endpoint-tag" 
                    size="large"
                    closable
                    effect="plain"
                    @close="handleRemoveEndpoint(element)"
                  >
                    <el-icon class="drag-handle"><Operation /></el-icon>
                    {{ element.name }}
                  </el-tag>
                </div>
              </template>
            </draggable>
          </div>
        </el-form-item>

        <el-form-item v-if="selectedForwardEndpoints.length > 1" label="Balance Strategy" prop="balance_strategy">
          <el-select
            v-model="modifyForm.balance_strategy"
            placeholder="Select balance strategy"
            style="width: 100%"
          >
            <el-option
              v-for="strategy in balanceStrategies"
              :key="strategy.value"
              :label="strategy.label"
              :value="strategy.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="subscriptionStore.allowForwardEndpoint && showForwardEndpoints" label="Tot Servers" prop="tot_server_list">
          <el-select
            v-model="selectedTotServerIds"
            multiple
            filterable
            placeholder="Select tot servers"
            style="width: 100%"
            @change="handleTotServerChange"
          >
            <el-option
              v-for="endpoint in availableTotServers"
              :key="endpoint.id"
              :label="endpoint.name"
              :value="endpoint.id"
            >
              <span>{{ endpoint.name }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="selectedTotServers.length > 0" label="Priority Order">
          <div class="forward-endpoints-list">
            <draggable
              v-model="selectedTotServers"
              @end="handleTotServerSort"
              item-key="id"
              :animation="150"
              class="draggable-list"
            >
              <template #item="{ element }">
                <div class="forward-endpoint-item">
                  <el-tag 
                    class="endpoint-tag" 
                    size="large"
                    closable
                    effect="plain"
                    @close="handleRemoveTotServer(element)"
                  >
                    <el-icon class="drag-handle"><Operation /></el-icon>
                    {{ element.name }}
                  </el-tag>
                </div>
              </template>
            </draggable>
          </div>
        </el-form-item>

        <el-form-item v-if="selectedTotServers.length > 1" label="Balance Strategy" prop="balance_strategy">
          <el-select
            v-model="modifyForm.tot_server_select_mode"
            placeholder="Select balance strategy"
            style="width: 100%"
          >
            <el-option
              v-for="strategy in balanceStrategies"
              :key="strategy.value"
              :label="strategy.label"
              :value="strategy.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="selectedTotServers.length > 1" label="Tot Server Test Method" prop="tot_server_test_method">
          <el-select v-model="form.tot_server_test_method" placeholder="Select test method" style="width: 100%">
            <el-option label="TCP Ping" :value="0" />
            <el-option label="ICMP" :value="1" />
          </el-select>
        </el-form-item>
        
        <!-- 添加网络拓扑图 -->
        <div class="network-topology" v-if="modifyForm.outbound_endpoint_id">
          <div class="topology-title">
            <el-icon><Connection /></el-icon>
            <span>Network Topology</span>
          </div>
          <div class="topology-container">
            <svg class="topology-svg" :viewBox="'0 0 800 ' + Math.max(120, selectedForwardEndpoints.length * 80)">
              <!-- 定义箭头 -->
              <defs>
                <marker
                  id="arrow"
                  viewBox="0 0 10 10"
                  refX="9"
                  refY="5"
                  markerWidth="6"
                  markerHeight="6"
                  orient="auto">
                  <path d="M 0 0 L 10 5 L 0 10 z" class="arrow-path"/>
                </marker>
              </defs>

              <!-- Line Node -->
              <g class="node line-node">
                <rect x="50" 
                      :y="selectedForwardEndpoints.length ? (centerY - 20) : 40" 
                      width="120" 
                      height="40" 
                      rx="5" />
                <text x="110" 
                      :y="selectedForwardEndpoints.length ? (centerY + 5) : 65" 
                      text-anchor="middle">{{ getLineName(modifyForm.outbound_endpoint_id) }}</text>
              </g>

              <!-- Forward Endpoints -->
              <template v-if="selectedForwardEndpoints.length > 0">
                <g v-for="(endpoint, index) in selectedForwardEndpoints" :key="endpoint.id">
                  <!-- Endpoint Node -->
                  <g class="node endpoint-node">
                    <rect x="340" 
                          :y="startY + index * 60 - 20" 
                          width="120" 
                          height="40" 
                          rx="5" />
                    <text x="400" 
                          :y="startY + index * 60 + 5" 
                          text-anchor="middle">{{ endpoint.name }}</text>
                  </g>

                  <!-- Left Connection -->
                  <path :d="'M 170 ' + centerY + 
                           ' L 250 ' + centerY + 
                           ' L 250 ' + (startY + index * 60) + 
                           ' L 340 ' + (startY + index * 60)"
                        class="connection-line" 
                        marker-end="url(#arrow)" />
                  
                  <!-- Right Connection -->
                  <path :d="'M 460 ' + (startY + index * 60) + 
                           ' L 550 ' + (startY + index * 60) + 
                           ' L 550 ' + centerY + 
                           ' L 630 ' + centerY"
                        class="connection-line" 
                        marker-end="url(#arrow)" />
                </g>
              </template>
              <template v-else>
                <!-- Direct Connection -->
                <path d="M 170 60 L 630 60" class="connection-line" marker-end="url(#arrow)" />
              </template>

              <!-- Target Node -->
              <g class="node target-node">
                <rect x="630" 
                      :y="selectedForwardEndpoints.length ? (centerY - 20) : 40" 
                      width="120" 
                      height="40" 
                      rx="5" />
                <text x="690" 
                      :y="selectedForwardEndpoints.length ? (centerY + 5) : 65" 
                      text-anchor="middle">
                  {{ modifyForm.target_address_list[0] }}:{{ modifyForm.target_port_v4 }}
                </text>
              </g>
            </svg>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="modifyDialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="handleModifySubmit" :loading="modifying">
            Save
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="latencyDialogVisible"
      :title="latencyTestSuccess ? 'Latency Test Result' : 'Latency Test Error'"
      width="500px"
    >
      <el-alert
        v-if="!latencyTestSuccess"
        type="error"
        :closable="false"
        show-icon
        :title="latencyError.errorCode || 'Test Failed'"
      >
        {{ latencyError.message }}
      </el-alert>
      <pre v-else class="latency-result">{{ latencyResult }}</pre>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useThemeStore } from '../stores/theme'
import { getPorts, createPort, deletePort, getSubscriptionInfo, modifyPort, testLatency, getForwardEndpoints, getLineStats, suspendPort, resumePort } from '../api'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Edit, Plus, Timer, Operation, Connection, Monitor, Aim, ArrowUp, ArrowDown, CaretRight, VideoPause } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import { useSubscriptionStore } from '../stores/subscription'

// Theme store
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// Computed style for table headers - borderless design
const headerCellStyle = computed(() => ({
  background: isDark.value ? 'var(--theme-fill-dark)' : 'var(--theme-fill-light)',
  color: 'var(--theme-text-primary)',
  fontWeight: '600',
  border: 'none'
}))

const subscriptionStore = useSubscriptionStore()
const components = {
  draggable
}

const ports = ref([])
const lines = ref([])
const forwardEndpoints = ref([])
const selectedForwardEndpointIds = ref([])
const selectedForwardEndpoints = ref([])
const totServers = ref([])
const selectedTotServerIds = ref([])
const selectedTotServers = ref([])
const allTotServers = ref([]) // Global tot servers data for mapping IDs to names
const allForwardEndpoints = ref([]) // Global forward endpoints data loaded once on mount
const loading = ref(false)
const dialogVisible = ref(false)
const creating = ref(false)
const formRef = ref(null)
const modifyDialogVisible = ref(false)
const modifying = ref(false)
const modifyFormRef = ref(null)
const portRangePlaceholder = ref('Leave empty for auto-assign')
const showForwardEndpoints = ref(false)
const latencyDialogVisible = ref(false)
const latencyResult = ref('')
const latencyTestSuccess = ref(true)
const latencyTestError = ref('')
const latencyData = ref({
  fwd_server_latency: null,
  remote_latency: {}
})
const testingLatencyIds = ref(new Set())
const suspendingPortIds = ref(new Set())
const tooltipVisibleMap = ref(new Map())
const mouseEnterTimer = ref(null)
const isEdit = ref(false)  // 添加 isEdit 的定义
const targetAddressText = ref('')
const modifyTargetAddressText = ref('')

const balanceStrategies = ref([
  { label: 'Best Latency', value: 0 },
  { label: 'Fallback', value: 1 },
  { label: 'Domain Follow', value: 2 },
  { label: 'Round Robin', value: 3 },
  { label: 'Random', value: 4 }
])

let isComponentMounted = true

// Pagination state
const currentPage = ref(1)
const pageSize = ref(20)
const totalItems = ref(0)
const totalPages = ref(1)
const pageSizeOptions = [20, 50, 100, 200]

// Search state
const searchForm = ref({
  id: '',
  name: '',
  line: null,
  entryPoint: '',
  target: ''
})
const searchTimeout = ref(null)

// Search UI state
const searchExpanded = ref(false)

// Load search expanded state from localStorage on component mount
const loadSearchExpandedState = () => {
  const saved = localStorage.getItem('port-search-expanded')
  if (saved !== null) {
    searchExpanded.value = JSON.parse(saved)
  }
}

// Save search expanded state to localStorage
const saveSearchExpandedState = () => {
  localStorage.setItem('port-search-expanded', JSON.stringify(searchExpanded.value))
}

// Line filtering state and methods
const lineFilterKeyword = ref('')
const filteredLines = ref([])

// Filter lines based on search keyword (case-insensitive)
const filterLines = (keyword) => {
  lineFilterKeyword.value = keyword
  if (!keyword) {
    filteredLines.value = lines.value
  } else {
    filteredLines.value = lines.value.filter(line =>
      line.display_name.toLowerCase().includes(keyword.toLowerCase())
    )
  }
}

// Initialize filtered lines when lines data changes
const initializeFilteredLines = () => {
  filteredLines.value = lines.value
}

// Computed properties for search UI
const hasActiveFilters = computed(() => {
  return !!(
    searchForm.value.id?.trim() ||
    searchForm.value.name?.trim() ||
    searchForm.value.line ||
    searchForm.value.entryPoint?.trim() ||
    searchForm.value.target?.trim()
  )
})

const activeFiltersCount = computed(() => {
  let count = 0
  if (searchForm.value.id?.trim()) count++
  if (searchForm.value.name?.trim()) count++
  if (searchForm.value.line) count++
  if (searchForm.value.entryPoint?.trim()) count++
  if (searchForm.value.target?.trim()) count++
  return count
})

// Computed property to combine totServers and allTotServers for dropdown options
const availableTotServers = computed(() => {
  const serverMap = new Map()

  // Add servers from totServers (current line's servers)
  totServers.value.forEach(server => {
    serverMap.set(server.id, server)
  })

  // Add servers from allTotServers (global servers) as fallback
  allTotServers.value.forEach(server => {
    if (!serverMap.has(server.id)) {
      serverMap.set(server.id, server)
    }
  })

  // Add any servers that are currently selected but not in the above lists
  selectedTotServerIds.value.forEach(id => {
    if (!serverMap.has(id)) {
      serverMap.set(id, { id, name: `Server ${id}` })
    }
  })

  return Array.from(serverMap.values()).sort((a, b) => a.name.localeCompare(b.name))
})

const form = ref({
  display_name: '',
  expected_port: '',
  target_address_list: [],
  target_select_mode: 0,
  test_method: 0,
  outbound_endpoint_id: '',
  balance_strategy: null,
  forward_endpoints: [],
  tot_server_list: [],
  tot_server_select_mode: 0,
  tot_server_test_method: 0
})

const modifyForm = ref({
  id: null,
  display_name: '',
  expected_port: null,
  target_address_list: [],
  target_select_mode: 0,
  test_method: 0,
  balance_strategy: null,
  forward_endpoints: [],
  tot_server_list: [],
  tot_server_select_mode: 0,
  tot_server_test_method: 0
})

const rules = {
  display_name: [
    { required: true, message: 'Please enter name', trigger: 'blur' }
  ],
  outbound_endpoint_id: [
    { required: true, message: 'Please select line', trigger: 'change' }
  ],
  target_address_list: [
    {
      validator(rule, value, callback) {
        if (!value || value.length === 0) {
          callback(new Error('Please enter target address'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  target_select_mode: [
    {
      validator(rule, value, callback) {
        if (value === null || value === undefined) {
          callback(new Error('Please select target select mode'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  test_method: [
    {
      validator(rule, value, callback) {
        if (value === null || value === undefined) {
          callback(new Error('Please select test method'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  tot_server_test_method: [
    {
      validator(rule, value, callback) {
        if (value === null || value === undefined) {
          callback(new Error('Please select test method'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
  expected_port: [
    {
      validator(rule, value, callback) {
        if (!value) {
          callback()
          return
        }
        const port = parseInt(value)
        if (isNaN(port) || port < 1 || port > 65535) {
          callback(new Error('Port must be between 1 and 65535'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  forward_endpoints: [
    {
      validator(rule, value, callback) {
        callback()
      },
      trigger: 'change'
    }
  ],
  balance_strategy: [
    {
      validator(rule, value, callback) {
        if (selectedForwardEndpoints.value.length > 1 && value === null) {
          callback(new Error('Please select a balance strategy'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

const fetchData = async (page = currentPage.value, size = pageSize.value) => {
  if (!isComponentMounted) return

  loading.value = true
  try {
    // Prepare search parameters
    const searchParams = {
      page,
      page_size: size
    }

    // Add search criteria if they exist
    console.log('Search form values:', searchForm.value)
    console.log('Lines data:', lines.value)

    if (searchForm.value.id?.trim()) {
      searchParams.id = parseInt(searchForm.value.id.trim())
      console.log('ID filter applied:', searchParams.id)
    }
    if (searchForm.value.name?.trim()) {
      searchParams.name = searchForm.value.name.trim()
    }
    if (searchForm.value.line) {
      searchParams.line = searchForm.value.line
      console.log('Line filter applied:', searchForm.value.line)
    }
    if (searchForm.value.entryPoint?.trim()) {
      searchParams.entry_point = searchForm.value.entryPoint.trim()
    }
    if (searchForm.value.target?.trim()) {
      searchParams.target = searchForm.value.target.trim()
    }

    console.log('Final search params:', searchParams)

    const [portsResponse, linesResponse] = await Promise.all([
      getPorts(searchParams),
      getSubscriptionInfo()
    ])

    // Handle paginated response
    if (portsResponse.data.ports) {
      // New paginated response format
      ports.value = portsResponse.data.ports
      currentPage.value = portsResponse.data.pagination.current_page
      pageSize.value = portsResponse.data.pagination.page_size
      totalItems.value = portsResponse.data.pagination.total_items
      totalPages.value = portsResponse.data.pagination.total_pages
    } else {
      // Fallback for old response format (if backend doesn't support pagination yet)
      ports.value = portsResponse.data
      totalItems.value = portsResponse.data.length
      totalPages.value = 1
    }

    lines.value = linesResponse.data.lines
    // Initialize filtered lines after fetching lines data
    initializeFilteredLines()
    console.log('Fetched lines:', lines.value)
    console.log('Pagination info:', { currentPage: currentPage.value, pageSize: pageSize.value, totalItems: totalItems.value, totalPages: totalPages.value })

    // 初始化每个端口的 tooltip 状态
    ports.value.forEach(port => {
      tooltipVisibleMap.value.set(port.id + '_modify', false)
      tooltipVisibleMap.value.set(port.id + '_suspend', false)
      tooltipVisibleMap.value.set(port.id + '_test', false)
      tooltipVisibleMap.value.set(port.id + '_delete', false)
      tooltipVisibleMap.value.set(port.id + '_target', false)
    })
  } catch (error) {
    console.error('Error fetching data:', error)
    ElMessage.error('Failed to fetch data')
  } finally {
    loading.value = false
  }
}

// Pagination handlers
const handlePageChange = (page) => {
  currentPage.value = page
  fetchData(page, pageSize.value)
}

const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1 // Reset to first page when changing page size
  fetchData(1, size)
}

// Search handlers
const handleSearchInput = () => {
  // Debounce search input to avoid too many API calls
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  searchTimeout.value = setTimeout(() => {
    handleSearch()
  }, 500)
}

const handleSearchChange = () => {
  // Immediate search for dropdown changes
  handleSearch()
}

const handleSearch = () => {
  currentPage.value = 1 // Reset to first page when searching
  fetchData(1, pageSize.value)
}

const handleClearSearch = () => {
  searchForm.value = {
    id: '',
    name: '',
    line: null,
    entryPoint: '',
    target: ''
  }
  currentPage.value = 1
  fetchData(1, pageSize.value)
}

// Search UI handlers
const toggleSearchExpanded = () => {
  searchExpanded.value = !searchExpanded.value
  saveSearchExpandedState()
}

const handleLineChange = async (lineId) => {
  console.log('Line changed:', lineId)

  if (!lineId) {
    showForwardEndpoints.value = false
    forwardEndpoints.value = []
    selectedForwardEndpointIds.value = []
    selectedForwardEndpoints.value = []
    totServers.value = []
    selectedTotServers.value = []
    selectedTotServerIds.value = []
    form.value.forward_endpoints = []
    portRangePlaceholder.value = 'Leave empty for auto-assign'
    return
  }
  // using line info to set port range placeholder
  let line = lines.value.find(line => line.id === lineId)
  if (line) {
    portRangePlaceholder.value = 'port range: ' + line.port_start + '-' + line.port_end
  }
  // 如果line信息中的allow_forward, 则显示forward endpoints
  if (subscriptionStore.allowForwardEndpoint && lines.value.find(line => line.id === lineId)?.allow_forward) {
    console.log('allow forward endpoint', subscriptionStore.allowForwardEndpoint , lines.value.find(line => line.id === lineId)?.allow_forward)
    showForwardEndpoints.value = true

    // Use globally loaded forward endpoints data instead of making API call
    if (allForwardEndpoints.value && allForwardEndpoints.value.length > 0) {
      console.log('Using globally loaded forward endpoints data for line change')
      forwardEndpoints.value = allForwardEndpoints.value.map(item => ({
        id: item.id,
        name: item.display_name || item.name
      }))
      totServers.value = allForwardEndpoints.value.map(item => ({
        id: item.id,
        name: item.display_name || item.name
      }))
    } else {
      console.warn('Global forward endpoints data not available')
      forwardEndpoints.value = []
      totServers.value = []
    }
  } else {
    showForwardEndpoints.value = false
    forwardEndpoints.value = []
    selectedForwardEndpointIds.value = []
    selectedForwardEndpoints.value = []
    totServers.value = []
    selectedTotServers.value = []
    selectedTotServerIds.value = []
    form.value.forward_endpoints = []
    form.value.tot_server_list = []
  }

}

const handleForwardEndpointSort = () => {
  selectedForwardEndpointIds.value = selectedForwardEndpoints.value.map(ep => ep.id)
}

const handleTotServerSort = () => {
  selectedTotServerIds.value = selectedTotServers.value.map(ep => ep.id)
}

const handleForwardEndpointChange = (selectedIds) => {
  selectedForwardEndpointIds.value = selectedIds
  selectedForwardEndpoints.value = selectedIds.map(id => 
    forwardEndpoints.value.find(endpoint => endpoint.id === id)
  ).filter(Boolean)
  
  // Set balance strategy default when multiple endpoints are selected
  if (selectedForwardEndpoints.value.length > 1) {
    if (!isEdit.value) {
      form.value.balance_strategy = 0 // Best Latency
    } else {
      modifyForm.value.balance_strategy = 0 // Best Latency
    }
  }
}

const handleTotServerChange = (selectedIds) => {
  selectedTotServerIds.value = selectedIds
  console.log('selectedIds', selectedIds)
  selectedTotServers.value = availableTotServers.value.filter(
    endpoint => selectedIds.includes(endpoint.id)
  )
  console.log('selectedTotServers', selectedTotServers.value)
  // Set balance strategy default when multiple endpoints are selected
  if (selectedTotServers.value.length > 1) {
    if (!isEdit.value) {
      form.value.tot_server_select_mode = 0 // Best Latency
    } else {
      modifyForm.value.tot_server_select_mode = 0 // Best Latency
    }
  }
}

const handleRemoveEndpoint = (endpoint) => {
  const index = selectedForwardEndpoints.value.findIndex(ep => ep.id === endpoint.id)
  if (index > -1) {
    selectedForwardEndpoints.value.splice(index, 1)
    selectedForwardEndpointIds.value = selectedForwardEndpoints.value.map(ep => ep.id)
  }
  
  if (selectedForwardEndpoints.value.length <= 1) {
    modifyForm.value.balance_strategy = 0
  }
}

const handleRemoveTotServer = (endpoint) => {
  const index = selectedTotServers.value.findIndex(ep => ep.id === endpoint.id)
  if (index > -1) {
    selectedTotServers.value.splice(index, 1)
    selectedTotServerIds.value = selectedTotServers.value.map(ep => ep.id)
  }
  
  if (selectedTotServers.value.length <= 1) {
    modifyForm.value.tot_server_select_mode = 0
  }
}

const handleCreate = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    creating.value = true
    const portData = {
      display_name: form.value.display_name,
      target_address_list: form.value.target_address_list,
      target_select_mode: form.value.target_select_mode,
      test_method: form.value.test_method,
      tot_server_test_method: form.value.tot_server_test_method,
      outbound_endpoint_id: form.value.outbound_endpoint_id,
    }

    if (form.value.expected_port) {
      portData.expected_port = parseInt(form.value.expected_port)
    }

    if (showForwardEndpoints.value && selectedForwardEndpoints.value.length > 0) {
      portData.forward_endpoints = selectedForwardEndpoints.value.map(endpoint => endpoint.id)
      if (selectedForwardEndpoints.value.length > 1) {
        portData.balance_strategy = form.value.balance_strategy
      }
    }

    if (showForwardEndpoints.value && selectedTotServers.value.length > 0) {
      portData.tot_server_list = selectedTotServers.value.map(endpoint => endpoint.id)
      if (selectedTotServers.value.length > 1) {
        portData.tot_server_select_mode = form.value.tot_server_select_mode
      }
      portData.tot_server_test_method = form.value.tot_server_test_method
    }
    
    console.log('Creating port with data:', portData)
    
    const response = await createPort(portData)
    console.log('Port created:', response)
    
    ElMessage({
      message: 'Port created successfully',
      type: 'success'
    })
    
    dialogVisible.value = false
    // After creating a new port, go to first page to see the new port (since it's ordered by newest first)
    currentPage.value = 1
    fetchData(1, pageSize.value)
  } catch (error) {
    console.error('Failed to create port:', error)
    ElMessage.error(error.message || 'Failed to create port')
  } finally {
    creating.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `Are you sure you want to delete port "${row.display_name}" (${row.ip_addr}:${row.port_v4})?`,
      'Delete Port',
      {
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )
    
    loading.value = true
    await deletePort(row.id)
    
    if (!isComponentMounted) return
    
    ElMessage.success(`Port "${row.display_name}" deleted successfully`)
    // After deletion, check if current page is empty and go to previous page if needed
    const itemsOnCurrentPage = totalItems.value - (currentPage.value - 1) * pageSize.value
    if (itemsOnCurrentPage <= 1 && currentPage.value > 1) {
      currentPage.value = currentPage.value - 1
    }
    await fetchData(currentPage.value, pageSize.value)
  } catch (error) {
    if (!isComponentMounted) return
    if (error !== 'cancel') {
      console.error('Delete port error:', error)
      ElMessage.error(
        error?.response?.data?.message || 
        error?.message || 
        'Failed to delete port'
      )
    }
  } finally {
    if (isComponentMounted) {
      loading.value = false
    }
  }
}

const handleSuspendResume = async (row) => {
  try {
    const action = row.is_suspended ? 'resume' : 'suspend'
    const actionText = row.is_suspended ? 'Resume' : 'Suspend'

    await ElMessageBox.confirm(
      `Are you sure you want to ${action} port "${row.display_name}" (${row.ip_addr}:${row.port_v4})?`,
      `${actionText} Port`,
      {
        confirmButtonText: actionText,
        cancelButtonText: 'Cancel',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    suspendingPortIds.value.add(row.id)

    if (row.is_suspended) {
      await resumePort(row.id)
    } else {
      await suspendPort(row.id)
    }

    if (!isComponentMounted) return

    ElMessage.success(`Port "${row.display_name}" ${action}ed successfully`)
    await fetchData(currentPage.value, pageSize.value)
  } catch (error) {
    if (!isComponentMounted) return
    if (error !== 'cancel') {
      console.error(`${row.is_suspended ? 'Resume' : 'Suspend'} port error:`, error)
      ElMessage.error(
        error?.response?.data?.message ||
        error?.message ||
        `Failed to ${row.is_suspended ? 'resume' : 'suspend'} port`
      )
    }
  } finally {
    if (isComponentMounted) {
      suspendingPortIds.value.delete(row.id)
    }
  }
}

const handleModify = async (row) => {
  // Check if user has forward endpoint permission and line allows forward endpoints
  if (subscriptionStore.allowForwardEndpoint && lines.value.find(line => line.id === row.outbound_endpoint_id)?.allow_forward) {
    showForwardEndpoints.value = true

    // Use globally loaded forward endpoints data instead of making API call
    if (allForwardEndpoints.value && allForwardEndpoints.value.length > 0) {
      console.log('Using globally loaded forward endpoints data for edit dialog')

      // Create endpoint map from global data
      const endpointMap = new Map(allForwardEndpoints.value.map(item => [
        item.id,
        {
          id: item.id,
          name: item.display_name || item.name
        }
      ]))

      // Set current form's forward endpoints and tot servers from global data
      forwardEndpoints.value = allForwardEndpoints.value.map(item => ({
        id: item.id,
        name: item.display_name || item.name
      }))
      totServers.value = allForwardEndpoints.value.map(item => ({
        id: item.id,
        name: item.display_name || item.name
      }))

      // Map selected forward endpoints using global data
      if (row.forward_endpoints && Array.isArray(row.forward_endpoints)) {
        selectedForwardEndpointIds.value = [...row.forward_endpoints]
        selectedForwardEndpoints.value = row.forward_endpoints
          .map(id => {
            const endpoint = endpointMap.get(id)
            if (endpoint) {
              return endpoint
            }
            // Fallback: create placeholder if endpoint not found
            console.warn(`Forward endpoint ${id} not found in global data, creating placeholder`)
            return { id, name: `Endpoint ${id}` }
          })
          .filter(Boolean)
      } else {
        selectedForwardEndpointIds.value = []
        selectedForwardEndpoints.value = []
      }

      // Map selected tot servers using global data
      if (row.tot_server_list && Array.isArray(row.tot_server_list)) {
        selectedTotServerIds.value = [...row.tot_server_list]
        selectedTotServers.value = row.tot_server_list
          .map(id => {
            const server = endpointMap.get(id)
            if (server) {
              return server
            }
            // Fallback: create placeholder if server not found
            console.warn(`Tot server ${id} not found in global data, creating placeholder`)
            return { id, name: `Server ${id}` }
          })
          .filter(Boolean)
      } else {
        selectedTotServerIds.value = []
        selectedTotServers.value = []
      }
    } else {
      // Fallback: if global data is not available, show warning and reset selections
      console.warn('Global forward endpoints data not available, resetting selections')
      forwardEndpoints.value = []
      totServers.value = []
      selectedForwardEndpointIds.value = []
      selectedForwardEndpoints.value = []
      selectedTotServerIds.value = []
      selectedTotServers.value = []
    }
  } else {
    // User doesn't have permission or line doesn't allow forward endpoints
    showForwardEndpoints.value = false
    forwardEndpoints.value = []
    totServers.value = []
    selectedForwardEndpointIds.value = []
    selectedForwardEndpoints.value = []
    selectedTotServerIds.value = []
    selectedTotServers.value = []
  }

  modifyForm.value = {
    id: row.id,
    display_name: row.display_name,
    expected_port: row.expected_port,
    target_address_list: row.target_address_list,
    target_select_mode: row.target_select_mode,
    test_method: row.test_method,
    balance_strategy: row.balance_strategy ?? 0,
    forward_endpoints: row.forward_endpoints,
    outbound_endpoint_id: row.outbound_endpoint_id,
    tot_server_list: row.tot_server_list,
    tot_server_select_mode: row.tot_server_select_mode,
    tot_server_test_method: row.tot_server_test_method,
  }

  modifyTargetAddressText.value = row.target_address_list.join('\n')

  modifyDialogVisible.value = true
}

const handleModifyDialogClosed = () => {
  if (modifyFormRef.value) {
    modifyFormRef.value.resetFields()
  }
  modifyForm.value = {
    id: null,
    display_name: '',
    expected_port: null,
    target_address_list: [],
    target_select_mode: 0,
    test_method: 0,
    balance_strategy: null,
    forward_endpoints: [],
    outbound_endpoint_id: null,
    tot_server_list: [],
    tot_server_select_mode: 0,
    tot_server_test_method: 0,
  }
  modifyTargetAddressText.value = ''
  selectedForwardEndpointIds.value = []
  selectedForwardEndpoints.value = []
  selectedTotServerIds.value = []
  selectedTotServers.value = []
}

const handleModifySubmit = async () => {
  if (!modifyFormRef.value) return
  
  await modifyFormRef.value.validate(async (valid) => {
    if (!valid) return
    
    modifying.value = true
    const portData = {
      id: modifyForm.value.id,
      display_name: modifyForm.value.display_name,
      target_address_list: modifyForm.value.target_address_list,
      target_select_mode: modifyForm.value.target_select_mode,
      test_method: modifyForm.value.test_method,
      balance_strategy: selectedForwardEndpoints.value.length > 1 ? modifyForm.value.balance_strategy : null,
      forward_endpoints: selectedForwardEndpointIds.value.length > 0 ? selectedForwardEndpointIds.value : null,
      tot_server_list: selectedTotServerIds.value.length > 0 ? selectedTotServerIds.value : null,
      tot_server_select_mode: selectedTotServers.value.length > 1 ? modifyForm.value.tot_server_select_mode : null,
      tot_server_test_method: modifyForm.value.tot_server_test_method,
      outbound_endpoint_id: modifyForm.value.outbound_endpoint_id
    }

    if (modifyForm.value.expected_port) {
      portData.expected_port = parseInt(modifyForm.value.expected_port)
    }
    
    try {
      await modifyPort(portData)
      ElMessage.success('Port modified successfully')
      modifyDialogVisible.value = false
      // Stay on current page after modification
      fetchData(currentPage.value, pageSize.value)
    } catch (error) {
      console.error('Failed to modify port:', error)
      ElMessage.error(error?.response?.data?.message || 'Failed to modify port')
    } finally {
      modifying.value = false
    }
  })
}

const getLineName = (lineId) => {
  if (!lineId || !lines.value) return 'N/A'
  const line = lines.value.find(l => l && l.id === lineId)
  return line ? line.display_name : 'N/A'
}

const getTotServerNames = (serverIds) => {
  if (!serverIds || !Array.isArray(serverIds) || serverIds.length === 0) {
    return []
  }
  if (!allTotServers.value || allTotServers.value.length === 0) {
    return serverIds.map(id => `Server ${id}`)
  }

  return serverIds.map(id => {
    const server = allTotServers.value.find(s => s && s.id === id)
    return server ? server.name : `Server ${id}`
  })
}

// Global function to fetch all forward endpoints once on mount
const fetchAllForwardEndpoints = async () => {
  // Only fetch if user has forward endpoint permission
  if (!subscriptionStore.allowForwardEndpoint) {
    console.log('User does not have forward endpoint permission, skipping global fetch')
    allForwardEndpoints.value = []
    allTotServers.value = []
    return
  }

  try {
    // Fetch all forward endpoints without pagination to get complete list
    console.log('Fetching complete forward endpoints list with fetch_all=true')
    const { data } = await getForwardEndpoints({ fetch_all: true })
    if (data && Array.isArray(data.forward_endpoints)) {
      // Store complete forward endpoints data globally
      allForwardEndpoints.value = data.forward_endpoints.map(item => ({
        id: item.id,
        name: item.display_name || item.name,
        display_name: item.display_name,
        // Store any other properties that might be needed
        ...item
      }))

      // Also update allTotServers for backward compatibility
      allTotServers.value = data.forward_endpoints.map(item => ({
        id: item.id,
        name: item.display_name || item.name
      }))

      console.log('Successfully loaded global forward endpoints:', allForwardEndpoints.value.length, 'items')
      console.log('Global forward endpoints data:', allForwardEndpoints.value)
    } else {
      console.warn('No forward endpoints data in response:', data)
      allForwardEndpoints.value = []
      allTotServers.value = []
    }
  } catch (error) {
    console.error('Failed to fetch global forward endpoints:', error)
    allForwardEndpoints.value = []
    allTotServers.value = []
  }
}

// Legacy function for backward compatibility - now uses global data
const fetchAllTotServers = async () => {
  // This function is now redundant as fetchAllForwardEndpoints handles both
  // but keeping it for any existing calls
  await fetchAllForwardEndpoints()
}

const formatTraffic = (bytes) => {
  if (bytes == null) return 'N/A'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`
}

const getTargetModeLabel = (mode) => {
  switch (mode) {
    case 0:
      return 'Best Latency'
    case 1:
      return 'Fallback'
    case 2:
      return 'Balance (Domain)'
    case 3:
      return 'Balance (Round Robin)'
    case 4:
      return 'Balance (Random)'
    default:
      return 'Unknown'
  }
}

const getTestMethodLabel = (method) => {
  switch (method) {
    case 0:
      return 'TCP Ping'
    case 1:
      return 'ICMP'
    default:
      return 'Unknown'
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.value = {
    display_name: '',
    expected_port: '',
    target_address_list: [],
    target_select_mode: 0,
    test_method: 0,
    tot_server_test_method: 0,
    outbound_endpoint_id: '',
    balance_strategy: null,
    forward_endpoints: []
  }
  targetAddressText.value = ''
  selectedForwardEndpointIds.value = []
  selectedForwardEndpoints.value = []
  showForwardEndpoints.value = false
}

const handleAddPort = () => {
  resetForm()
  dialogVisible.value = true
}

const handleDialogClosed = () => {
  resetForm()
}

const handleMouseEnter = (row, type = 'test') => {
  const key = row.id + '_' + type
  // 清除之前的定时器
  if (mouseEnterTimer.value) {
    clearTimeout(mouseEnterTimer.value)
  }
  // 设置新的定时器
  mouseEnterTimer.value = setTimeout(() => {
    tooltipVisibleMap.value.set(key, true)
  }, 500)
}

const handleMouseLeave = (row, type = 'test') => {
  const key = row.id + '_' + type
  // 清除定时器
  if (mouseEnterTimer.value) {
    clearTimeout(mouseEnterTimer.value)
  }
  tooltipVisibleMap.value.set(key, false)
}

const handleTestLatency = async (row) => {
  if (testingLatencyIds.value.has(row.id)) return;
  
  // 清除定时器
  if (mouseEnterTimer.value) {
    clearTimeout(mouseEnterTimer.value)
  }
  // 强制关闭所有 tooltip
  ports.value.forEach(port => {
    tooltipVisibleMap.value.set(port.id + '_modify', false)
    tooltipVisibleMap.value.set(port.id + '_test', false)
    tooltipVisibleMap.value.set(port.id + '_delete', false)
    tooltipVisibleMap.value.set(port.id + '_target', false)
  })
  
  testingLatencyIds.value.add(row.id)
  latencyTestSuccess.value = false;
  latencyTestError.value = '';
  
  try {
    const result = await testLatency(row.id);
    latencyTestSuccess.value = true;
    latencyResult.value = result.data.content;
    
  } catch (error) {
    latencyTestSuccess.value = false;
    latencyTestError.value = error.message || 'Failed to test latency';
  } finally {
    testingLatencyIds.value.delete(row.id);
    latencyDialogVisible.value = true;
  }
}

const centerY = computed(() => {
  const height = Math.max(120, selectedForwardEndpoints.value.length * 80);
  return height / 2;
});

const startY = computed(() => {
  if (selectedForwardEndpoints.value.length <= 1) return centerY.value;
  return centerY.value - (selectedForwardEndpoints.value.length - 1) * 30;
});

const handleTargetAddressInput = (value) => {
  form.value.target_address_list = value
    .split('\n')
    .map(line => line.trim())
    .filter(line => line !== '')
}

const handleModifyTargetAddressInput = (value) => {
  modifyForm.value.target_address_list = value
    .split('\n')
    .map(line => line.trim())
    .filter(line => line !== '')
}

const getLatencyClass = (latency) => {
  const ms = parseFloat(latency);
  if (ms < 50) return 'latency-excellent';
  if (ms < 100) return 'latency-good';
  if (ms < 200) return 'latency-fair';
  return 'latency-poor';
};

// Text truncation utility functions
const textElementRefs = ref(new Map());

const setTextRef = (el, key) => {
  if (el) {
    textElementRefs.value.set(key, el);
  }
};

const shouldShowTooltip = (text, maxWidth) => {
  if (!text) return false;

  // Create a temporary element to measure actual text width
  const tempElement = document.createElement('div');
  tempElement.style.position = 'absolute';
  tempElement.style.visibility = 'hidden';
  tempElement.style.whiteSpace = 'nowrap';
  tempElement.style.fontSize = '14px'; // Match table font size
  tempElement.style.fontFamily = 'inherit';
  tempElement.textContent = text;

  document.body.appendChild(tempElement);
  const actualWidth = tempElement.offsetWidth;
  document.body.removeChild(tempElement);

  // Add some padding to account for cell padding
  return actualWidth > (maxWidth - 20);
};

const getRemotesByServer = (server) => {
  return Object.entries(latencyData.value.remote_latency)
    .filter(([key]) => {
      const [fwd] = JSON.parse(key);
      return fwd === server;
    });
};

const getDirectRemotes = () => {
  return Object.entries(latencyData.value.remote_latency)
    .filter(([key]) => {
      const [fwd] = JSON.parse(key);
      return !fwd || !latencyData.value.fwd_server_latency?.[fwd];
    });
};

onMounted(async () => {
  isComponentMounted = true
  // Load search expanded state from localStorage
  loadSearchExpandedState()

  try {
    await subscriptionStore.fetchData()
    // Load global forward endpoints data once for the entire session
    await fetchAllForwardEndpoints()
    await fetchData()
  } catch (error) {
    console.error('Failed to fetch initial data:', error)
    ElMessage.error('Failed to fetch initial data')
  }
})

onUnmounted(() => {
  isComponentMounted = false
})
</script>

<style scoped>
.ports-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: var(--theme-text-primary);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Search Toggle Button Styles */
.search-toggle-btn {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 6px;
  font-weight: 500;
}

.search-toggle-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-toggle-text {
  margin-left: 4px;
  margin-right: 4px;
}

.filter-badge {
  margin-left: 8px;
}

.filter-badge :deep(.el-badge__content) {
  font-size: 11px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 8px;
}

/* Search Form Styles */
.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.search-form .el-form-item__label {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* Dark mode compatibility for search form */
:root.dark .search-container {
  background: var(--el-bg-color);
  border-color: var(--el-border-color);
}

:root.dark .search-toggle-btn:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .search-toggle-btn {
    width: 100%;
    justify-content: center;
  }

  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .el-form-item {
    width: 100%;
  }

  .search-form .el-input,
  .search-form .el-select {
    width: 100% !important;
  }
}

.port-form {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
  margin-top: 20px;
}

.forward-endpoints-list {
  width: 100%;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 8px;
  min-height: 80px;
  background-color: var(--el-bg-color);
}

.draggable-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.forward-endpoint-item {
  cursor: move;
  margin-bottom: 8px;
}

.endpoint-tag {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin: 0;
}

.drag-handle {
  cursor: move;
  color: var(--el-text-color-secondary);
  margin-right: 8px;
}

.latency-result {
  white-space: pre-wrap;
  font-family: monospace;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.network-topology {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid var(--theme-border-light);
  border-radius: 4px;
  background-color: var(--theme-bg-tertiary);
  transition: var(--theme-transition);
}

.topology-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: var(--theme-text-regular);
  font-size: 14px;
  font-weight: 500;
}

.topology-title .el-icon {
  font-size: 16px;
  color: var(--theme-primary);
}

.topology-container {
  width: 100%;
}

.topology-svg {
  width: 100%;
  height: auto;
}

.node rect {
  fill: var(--theme-bg-primary);
  stroke: var(--theme-primary);
  stroke-width: 2;
  transition: var(--theme-transition);
}

.node text {
  fill: var(--theme-text-regular);
  font-size: 12px;
}

.line-node rect {
  stroke: var(--theme-success);
}

.endpoint-node rect {
  stroke: var(--theme-primary);
}

.target-node rect {
  stroke: var(--theme-danger);
}

.connection-line {
  fill: none;
  stroke: var(--theme-primary);
  stroke-width: 2;
  transition: var(--theme-transition);
}

.arrow-path {
  fill: var(--theme-primary);
  transition: var(--theme-transition);
}

.target-address {
  cursor: pointer;
}

.more-addresses {
  color: #909399;
  font-size: 12px;
  margin-left: 4px;
}

.target-addresses-tooltip {
  white-space: pre-line;
}

.target-address-item {
  margin-bottom: 8px;
}

.target-address-input {
  width: 100%;
}

.target-mode {
  margin-top: 4px;
}

.target-mode .el-tag {
  margin-right: 4px;
}

.latency-results {
  padding: 20px;
}

.network-diagram {
  display: flex;
  justify-content: center;
}

.entry-point {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
}

.forward-servers {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;
  justify-content: center;
}

.server-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  position: relative;
}

.remote-targets {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.remote-targets.direct {
  position: relative;
}

.remote-targets.direct::before {
  content: '';
  position: absolute;
  width: 2px;
  height: 40px;
  background: var(--el-border-color);
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
}

.node {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  min-width: 200px;
  position: relative;
}

.node.entry {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary);
}

.node .el-icon {
  color: var(--el-color-primary);
}

.node .latency {
  margin-left: auto;
  font-family: monospace;
}

.latency-excellent { color: #67c23a; }
.latency-good { color: #409eff; }
.latency-fair { color: #e6a23c; }
.latency-poor { color: #f56c6c; }

.server-group::before,
.remote-targets .node::before {
  content: '';
  position: absolute;
  width: 2px;
  background: var(--el-border-color);
  left: 50%;
  transform: translateX(-50%);
}

.server-group::before {
  top: -40px;
  height: 40px;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 0 20px;
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.page-size-selector {
  width: 120px;
}

.pagination-controls {
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
  }
}

.remote-targets .node::before {
  top: -12px;
  height: 12px;
}

.connection-layout {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;
  justify-content: center;
}

.direct-connections {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.direct-connections::before {
  content: '';
  position: absolute;
  width: 2px;
  background: var(--el-border-color);
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
}

/* Suspended port styling */
.el-table .el-table__row.suspended-port {
  background-color: var(--el-color-warning-light-9);
  opacity: 0.7;
}

.el-table .el-table__row.suspended-port:hover {
  background-color: var(--el-color-warning-light-8) !important;
}

.suspended-port .el-table__cell {
  color: var(--el-color-warning-dark-2);
}

/* Dark mode support for suspended ports */
.dark .el-table .el-table__row.suspended-port {
  background-color: rgba(230, 162, 60, 0.1);
}

.dark .el-table .el-table__row.suspended-port:hover {
  background-color: rgba(230, 162, 60, 0.15) !important;
}

.dark .suspended-port .el-table__cell {
  color: var(--el-color-warning-light-3);
}

/* Table row height consistency and text truncation styles */
.el-table .el-table__row {
  height: 60px !important;
}

.el-table .el-table__cell {
  padding: 8px 0 !important;
  vertical-align: middle;
}

.truncated-text {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  line-height: 1.4;
  cursor: default;
}

/* Only show help cursor when text is actually truncated */
.truncated-text.text-truncated:hover {
  cursor: help;
}

/* Traffic column specific styles */
.traffic-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  gap: 2px;
}

.traffic-row {
  display: flex;
  align-items: center;
  gap: 4px;
  line-height: 1.2;
}

.traffic-icon {
  font-weight: bold;
  color: var(--el-text-color-secondary);
  min-width: 12px;
}

.traffic-value {
  font-family: monospace;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* Tooltip styles for truncated text */
.truncated-text-tooltip {
  max-width: 300px;
  word-wrap: break-word;
}

/* Ensure consistent cell heights across all columns */
.el-table .el-table__cell .cell {
  display: flex;
  align-items: center;
  min-height: 44px;
  line-height: 1.4;
}

/* Table Card Styling */
.table-card {
  border-radius: 8px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-light);
  padding: 12px;
}

/* Clean borderless table styling matching other management pages */
:deep(.el-table) {
  border-radius: 8px;
  background-color: var(--theme-bg-primary);
  border: none;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-extra-light) !important;
}

:deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

/* Dark mode compatibility for traffic indicators */
:root.dark .traffic-icon {
  color: var(--el-text-color-secondary);
}

:root.dark .traffic-value {
  color: var(--el-text-color-regular);
}

/* Status indicator styles */
.status-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: help;
  transition: all 0.2s ease;
}

.status-dot:hover {
  transform: scale(1.2);
}

.status-active {
  background-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.status-suspended {
  background-color: #e6a23c;
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
}

/* Dark mode compatibility for status indicators */
:root.dark .status-active {
  background-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.3);
}

:root.dark .status-suspended {
  background-color: #e6a23c;
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.3);
}

/* Dark mode specific table styling */
:global(.dark) :deep(.el-table th) {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-light) !important;
}

:global(.dark) :deep(.el-table .el-table__empty-block) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-secondary) !important;
}

/* Preserve suspended port styling with new borderless design */
:deep(.el-table .el-table__row.suspended-port td) {
  background-color: var(--el-color-warning-light-9) !important;
  opacity: 0.7;
}

:deep(.el-table .el-table__row.suspended-port:hover td) {
  background-color: var(--el-color-warning-light-8) !important;
}

:global(.dark) :deep(.el-table .el-table__row.suspended-port td) {
  background-color: rgba(230, 162, 60, 0.1) !important;
}

:global(.dark) :deep(.el-table .el-table__row.suspended-port:hover td) {
  background-color: rgba(230, 162, 60, 0.15) !important;
}

:deep(.el-button.is-circle) {
  padding: 6px;
}
</style>